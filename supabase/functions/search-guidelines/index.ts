// Supabase Edge Function for searching dental guidelines
// Uses built-in Deno.serve (no remote imports needed)

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

async function searchGuidelinesHandler(request: Request): Promise<Response> {
  const startTime = Date.now();
  
  try {
    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    // Only allow POST requests
    if (request.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { 
          status: 405,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return new Response(
        JSON.stringify({ error: 'Invalid <PERSON><PERSON><PERSON> in request body' }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // Basic validation
    const { query, carrier, category, limit = 5 } = body;
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return new Response(
        JSON.stringify({ error: 'Query is required and must be a non-empty string' }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    console.log(`Searching guidelines for: "${query}"`);

    // Mock data for testing
    const mockResults = [
      {
        id: 1,
        title: `Guidelines for: ${query}`,
        category: category || 'General',
        carrier: carrier || 'Multiple Carriers',
        similarity_score: 0.85,
        content: `Mock guideline result for "${query}".`,
        relevance_score: 0.85,
        content_preview: `Mock guideline result for "${query}".`,
        match_type: 'high',
        keywords: query.toLowerCase().split(' ').slice(0, 3)
      }
    ];

    const duration = Date.now() - startTime;

    const responseData = {
      success: true,
      data: {
        results: mockResults.slice(0, limit),
        total_found: mockResults.length,
        search_metadata: {
          query,
          carrier_filter: carrier,
          category_filter: category,
          similarity_threshold: 0.2,
          processing_time_ms: duration,
          search_type: 'mock_data',
          quality: 'high_relevance'
        }
      }
    };

    return new Response(
      JSON.stringify(responseData),
      { 
        status: 200,
        headers: { 
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );

  } catch (error) {
    console.error('Error in search-guidelines function:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  }
}

Deno.serve(searchGuidelinesHandler);
