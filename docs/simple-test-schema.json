{"openapi": "3.1.0", "info": {"title": "Simple Test API", "version": "1.0.0"}, "servers": [{"url": "https://ymivwfdmeymosgvgoibb.supabase.co/functions/v1"}], "paths": {"/search-guidelines": {"post": {"summary": "Test Search", "operationId": "testSearch", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "example": "test query"}}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "object"}}}}}}}}}